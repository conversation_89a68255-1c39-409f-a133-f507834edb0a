import React, { createContext, useContext, ReactNode } from 'react';

// Types
interface User {
  email: string;
  iotToken: string;
}

interface AuthContextType {
  user: User | null;
  webToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<boolean>;
  signUp: (email: string, password: string, iotToken: string) => Promise<boolean>;
  signOut: () => Promise<void>;
  error: string | null;
  clearError: () => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  // Simple state - just keep loading forever for now
  const user = null;
  const webToken = null;
  const isLoading = true; // Always loading
  const error = null;
  const isAuthenticated = false;

  // Simple dummy functions
  const signIn = async (email: string, password: string): Promise<boolean> => {
    return false;
  };

  const signUp = async (email: string, password: string, iotToken: string): Promise<boolean> => {
    return false;
  };

  const signOut = async () => {
    // Do nothing
  };

  const clearError = () => {
    // Do nothing
  };

  const value: AuthContextType = {
    user,
    webToken,
    isLoading,
    isAuthenticated,
    signIn,
    signUp,
    signOut,
    error,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
