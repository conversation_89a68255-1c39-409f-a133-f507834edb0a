import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from '../services/api';

// Types
interface User {
  email: string;
  iotToken: string;
}

interface AuthContextType {
  user: User | null;
  webToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<boolean>;
  signUp: (email: string, password: string, iotToken: string) => Promise<boolean>;
  signOut: () => Promise<void>;
  error: string | null;
  clearError: () => void;
}

// Storage keys
const STORAGE_KEYS = {
  WEB_TOKEN: 'web_token',
  USER_DATA: 'user_data',
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [webToken, setWebToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = !!user && !!webToken;

  // Load stored authentication data on app start
  useEffect(() => {
    loadStoredAuth();
  }, []);

  const loadStoredAuth = async () => {
    try {
      console.log('Loading stored auth...');
      const [storedToken, storedUser] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.WEB_TOKEN),
        AsyncStorage.getItem(STORAGE_KEYS.USER_DATA),
      ]);

      console.log('Stored token:', !!storedToken);
      console.log('Stored user:', !!storedUser);

      if (storedToken && storedUser) {
        setWebToken(storedToken);
        setUser(JSON.parse(storedUser));
        console.log('User authenticated from storage');
      } else {
        console.log('No stored auth found');
      }
    } catch (error) {
      console.error('Error loading stored auth:', error);
    } finally {
      setIsLoading(false);
      console.log('Auth loading complete');
    }
  };

  const storeAuthData = async (token: string, userData: User) => {
    try {
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.WEB_TOKEN, token),
        AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData)),
      ]);
    } catch (error) {
      console.error('Error storing auth data:', error);
      throw new Error('Failed to store authentication data');
    }
  };

  const clearAuthData = async () => {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.WEB_TOKEN),
        AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA),
      ]);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  };

  const signIn = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiService.signIn(email, password);

      if (response.success && response.data) {
        const userData: User = {
          email,
          iotToken: response.data.user.iotToken,
        };

        await storeAuthData(response.data.webToken, userData);
        setWebToken(response.data.webToken);
        setUser(userData);
        return true;
      } else {
        setError(response.error || 'Invalid email or password');
        return false;
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setError('Network error. Please check your connection.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, iotToken: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiService.signUp(email, password, iotToken);

      if (response.success && response.data) {
        const userData: User = {
          email,
          iotToken,
        };

        await storeAuthData(response.data.webToken, userData);
        setWebToken(response.data.webToken);
        setUser(userData);
        return true;
      } else {
        setError(response.error || 'Registration failed');
        return false;
      }
    } catch (error) {
      console.error('Sign up error:', error);
      setError('Network error. Please check your connection.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await clearAuthData();
      setUser(null);
      setWebToken(null);
      setError(null);
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    webToken,
    isLoading,
    isAuthenticated,
    signIn,
    signUp,
    signOut,
    error,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
