import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  ScrollView,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../../context/AuthContext';
import { apiService, DeviceStatus } from '../../services/api';

export default function HomeScreen() {
  const { user, webToken, signOut } = useAuth();
  const [deviceStatus, setDeviceStatus] = useState<DeviceStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchDeviceStatus();
  }, []);

  const fetchDeviceStatus = async () => {
    if (!webToken) return;

    try {
      setIsLoading(true);
      const response = await apiService.getDeviceStatus(webToken);
      
      if (response.success && response.data) {
        setDeviceStatus(response.data);
        setLastUpdated(new Date());
      } else {
        Alert.alert('Error', response.error || 'Failed to fetch device status');
      }
    } catch (error) {
      console.error('Error fetching device status:', error);
      Alert.alert('Error', 'Failed to fetch device status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickToggle = async () => {
    if (!webToken || !deviceStatus) return;

    try {
      setIsLoading(true);
      const response = deviceStatus.isOn 
        ? await apiService.turnOffDevice(webToken)
        : await apiService.turnOnDevice(webToken);

      if (response.success) {
        // Refresh status after successful toggle
        await fetchDeviceStatus();
        Alert.alert(
          'Success', 
          `Device turned ${deviceStatus.isOn ? 'off' : 'on'} successfully`
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to toggle device');
      }
    } catch (error) {
      console.error('Error toggling device:', error);
      Alert.alert('Error', 'Failed to toggle device');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const formatLastUpdated = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isLoading} onRefresh={fetchDeviceStatus} />
      }>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.welcomeText}>Welcome back!</Text>
            <Text style={styles.emailText}>{user?.email}</Text>
          </View>
          <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
            <Icon name="logout" size={24} color="#f44336" />
          </TouchableOpacity>
        </View>

        {/* Device Status Card */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>Device Status</Text>
            <TouchableOpacity onPress={fetchDeviceStatus} disabled={isLoading}>
              <Icon name="refresh" size={24} color="#2196F3" />
            </TouchableOpacity>
          </View>

          {deviceStatus ? (
            <View style={styles.statusContent}>
              <View style={styles.statusIndicator}>
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: deviceStatus.isOn ? '#4CAF50' : '#f44336' },
                  ]}
                />
                <Text style={styles.statusText}>
                  {deviceStatus.isOn ? 'Online' : 'Offline'}
                </Text>
              </View>

              {deviceStatus.temperature && (
                <View style={styles.sensorData}>
                  <Icon name="thermostat" size={20} color="#FF9800" />
                  <Text style={styles.sensorText}>
                    {deviceStatus.temperature}°C
                  </Text>
                </View>
              )}

              {deviceStatus.humidity && (
                <View style={styles.sensorData}>
                  <Icon name="water-drop" size={20} color="#2196F3" />
                  <Text style={styles.sensorText}>
                    {deviceStatus.humidity}%
                  </Text>
                </View>
              )}

              <Text style={styles.lastUpdatedText}>
                Last updated: {formatLastUpdated(lastUpdated)}
              </Text>
            </View>
          ) : (
            <View style={styles.noDataContainer}>
              <Icon name="device-unknown" size={48} color="#ccc" />
              <Text style={styles.noDataText}>No device data available</Text>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity
            style={[
              styles.quickActionButton,
              deviceStatus?.isOn ? styles.turnOffButton : styles.turnOnButton,
            ]}
            onPress={handleQuickToggle}
            disabled={isLoading || !deviceStatus}>
            <Icon
              name={deviceStatus?.isOn ? 'power-off' : 'power'}
              size={24}
              color="#fff"
            />
            <Text style={styles.quickActionText}>
              Turn {deviceStatus?.isOn ? 'Off' : 'On'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Device Info */}
        <View style={styles.deviceInfo}>
          <Text style={styles.sectionTitle}>Device Information</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>IoT Token:</Text>
            <Text style={styles.infoValue}>
              {user?.iotToken ? `***${user.iotToken.slice(-4)}` : 'Not available'}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Connection:</Text>
            <Text style={[
              styles.infoValue,
              { color: deviceStatus ? '#4CAF50' : '#f44336' }
            ]}>
              {deviceStatus ? 'Connected' : 'Disconnected'}
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  emailText: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  signOutButton: {
    padding: 8,
  },
  statusCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusContent: {
    alignItems: 'center',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  sensorData: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sensorText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  lastUpdatedText: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noDataText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
  quickActions: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  turnOnButton: {
    backgroundColor: '#4CAF50',
  },
  turnOffButton: {
    backgroundColor: '#f44336',
  },
  quickActionText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  deviceInfo: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
});
