// API Configuration
const API_BASE_URL = 'http://localhost:3000/api'; // Replace with your actual API URL

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface AuthResponse {
  webToken: string;
  user: {
    email: string;
    iotToken: string;
  };
}

export interface DeviceStatus {
  isOn: boolean;
  lastUpdated: string;
  temperature?: number;
  humidity?: number;
}

export interface HistoryItem {
  id: string;
  action: 'turn_on' | 'turn_off' | 'status_check';
  timestamp: string;
  success: boolean;
  message?: string;
}

export interface ScheduleItem {
  id: string;
  action: 'turn_on' | 'turn_off';
  time: string;
  days: string[];
  enabled: boolean;
}

// API Service Class
class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (response.ok) {
        return {
          success: true,
          data,
        };
      } else {
        return {
          success: false,
          error: data.message || 'Request failed',
        };
      }
    } catch (error) {
      console.error('API Request Error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection.',
      };
    }
  }

  // Authentication APIs
  async signIn(email: string, password: string): Promise<ApiResponse<AuthResponse>> {
    return this.makeRequest<AuthResponse>('/auth/signin', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async signUp(email: string, password: string, iotToken: string): Promise<ApiResponse<AuthResponse>> {
    return this.makeRequest<AuthResponse>('/auth/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password, iotToken }),
    });
  }

  // Device Control APIs
  async turnOnDevice(webToken: string): Promise<ApiResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>('/device/turn-on', {
      method: 'POST',
    }, webToken);
  }

  async turnOffDevice(webToken: string): Promise<ApiResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>('/device/turn-off', {
      method: 'POST',
    }, webToken);
  }

  async getDeviceStatus(webToken: string): Promise<ApiResponse<DeviceStatus>> {
    return this.makeRequest<DeviceStatus>('/device/status', {
      method: 'GET',
    }, webToken);
  }

  // History APIs
  async getHistory(webToken: string, limit: number = 50): Promise<ApiResponse<HistoryItem[]>> {
    return this.makeRequest<HistoryItem[]>(`/device/history?limit=${limit}`, {
      method: 'GET',
    }, webToken);
  }

  // Schedule APIs
  async getSchedules(webToken: string): Promise<ApiResponse<ScheduleItem[]>> {
    return this.makeRequest<ScheduleItem[]>('/device/schedules', {
      method: 'GET',
    }, webToken);
  }

  async createSchedule(
    webToken: string,
    schedule: Omit<ScheduleItem, 'id'>
  ): Promise<ApiResponse<ScheduleItem>> {
    return this.makeRequest<ScheduleItem>('/device/schedules', {
      method: 'POST',
      body: JSON.stringify(schedule),
    }, webToken);
  }

  async updateSchedule(
    webToken: string,
    scheduleId: string,
    schedule: Partial<ScheduleItem>
  ): Promise<ApiResponse<ScheduleItem>> {
    return this.makeRequest<ScheduleItem>(`/device/schedules/${scheduleId}`, {
      method: 'PUT',
      body: JSON.stringify(schedule),
    }, webToken);
  }

  async deleteSchedule(webToken: string, scheduleId: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>(`/device/schedules/${scheduleId}`, {
      method: 'DELETE',
    }, webToken);
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Export default
export default apiService;
