#include "secret.h" 

void sendSMS(String message) {
  // Print the message and phone number
  Serial.println("To: " + phoneNumber + ", Message: " + message);

  // Blink LED to indicate SMS sending
  blinkLED(SMS_SENT_PIN);

  // Send the AT command to the SIM800 module
  sim800.println("AT+CMGS=\"+" + phoneNumber + "\"");
  delay(1000);

  // Convert the Arduino String to std::string for encryption
  std::string messageStr = std::string(message.c_str());

  // Encrypt the message
  std::string encryptedMessage = xorEncryptDecrypt(messageStr);  // Get the encrypted message

  // Convert the encrypted message back to Arduino String
  String encryptedMessageStr = String(encryptedMessage.c_str());

  // Concatenate the original message and encrypted message as a String
  String fullMessage = message + "---" + encryptedMessageStr;

  // Print the message and phone number
  Serial.println("To: " + phoneNumber + ", Message: " + fullMessage);

  // Send the SMS message along with the encrypted message
  sim800.print(fullMessage);  // Send the concatenated message

  // Wait for a short period to ensure SIM800 processes the command
  delay(1000);

  // Send Ctrl+Z to indicate the end of the message (SMS sending)
  sim800.write(26); // Ctrl+Z

  // Wait a few seconds for the SMS to be sent
  delay(3000);
}

void processSMS(String &buffer) {
    // Decrypt the SMS
    String smsText = buffer.trim();  // Trim whitespace
    std::string smsTextStr = std::string(smsText.c_str());
    std::string decryptedText = xorEncryptDecrypt(smsTextStr);

    Serial.println("Decrypted SMS: " + decryptedText.c_str());

    // Convert decrypted text back to Arduino String
    String decryptedMessage = String(decryptedText.c_str());

    // Split the decrypted message into parts
    int partCount = 0;
    String parts[3]; // mrc, command, and props
    while (decryptedMessage.length() > 0 && partCount < 3) {
        int idx = decryptedMessage.indexOf(':');
        if (idx == -1) {
            parts[partCount++] = decryptedMessage; // Add the last part
            break;
        } else {
            parts[partCount++] = decryptedMessage.substring(0, idx);
            decryptedMessage = decryptedMessage.substring(idx + 1);
        }
    }

    // Validate the structure
    if (partCount < 2 || parts[0] != "mrc") {
        Serial.println("Invalid SMS format or missing 'mrc' prefix. Ignored.");
        return; // Ignore invalid messages
    }

    // Extract command and optional properties
    String command = parts[1].trim(); // Second part is the command
    String props = (partCount == 3) ? parts[2].trim() : ""; // Third part, if present

    // List of valid commands
    String validCommands[] = {"on", "off", "status", "schedule"};
    bool isValidCommand = false;

    // Check if the command is valid
    for (String validCommand : validCommands) {
        if (command == validCommand) {
            isValidCommand = true;
            break;
        }
    }

    if (isValidCommand) {
        Serial.println("Valid command received: " + command);

        // Parse the props if present
        if (props.length() > 0) {
            Serial.println("Processing props: " + props);
            int propCount = 0;
            String propArray[10]; // Assume max 10 props for simplicity
            while (props.length() > 0) {
                int idx = props.indexOf('|');
                if (idx == -1) {
                    propArray[propCount++] = props.trim(); // Add the last prop
                    break;
                } else {
                    propArray[propCount++] = props.substring(0, idx).trim();
                    props = props.substring(idx + 1);
                }
            }

            // Print parsed properties
            for (int i = 0; i < propCount; i++) {
                Serial.println("Prop " + String(i + 1) + ": " + propArray[i]);
            }

            // Special handling for schedule command
            if (command == "schedule" && propCount > 0) {
                for (int i = 0; i < propCount; i++) {
                    int slashIdx = propArray[i].indexOf('/');
                    if (slashIdx != -1) {
                        String timestamp = propArray[i].substring(0, slashIdx);
                        String action = propArray[i].substring(slashIdx + 1);
                        Serial.println("Scheduled: " + action + " at " + timestamp);
                        // Add your scheduling logic here
                    } else {
                        Serial.println("Invalid schedule format in props.");
                    }
                }
            }
        }

        // Perform action based on the command
        if (command == "on") {
            digitalWrite(RELAY_PIN, HIGH);  // Turn on the relay
            sendSMS("status:on");
        } else if (command == "off") {
            digitalWrite(RELAY_PIN, LOW);  // Turn off the relay
            sendSMS("status:off");
        } else if (command == "status") {
            String status = digitalRead(RELAY_PIN) == HIGH ? "on" : "off";
            sendSMS("status:" + status);
        }
    } else {
        Serial.println("Invalid command: " + command);
    }

    // Clear the buffer after processing
    buffer = "";
}