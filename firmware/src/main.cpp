#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <HardwareSerial.h>
#include "secret.h"

// Define pins
#define SMS_RECEIVED_PIN 32 // RED LED
#define SMS_SENT_PIN 33     // BLUE LED
#define RELAY_PIN 25        // <PERSON><PERSON><PERSON> LED

// Define UART pins for SIM800L
#define RX_PIN 26  // ESP32 RX connected to SIM800L TX
#define TX_PIN 27  // ESP32 TX connected to SIM800L RX

HardwareSerial sim800(1);

#define BUFFER_SIZE 1024
char serialBuffer[BUFFER_SIZE];

String phoneNumber = "4915754181256";

void blinkLED(int pin) {
  for (int i = 0; i < 5; i++) {
    digitalWrite(pin, HIGH);
    delay(100);
    digitalWrite(pin, LOW);
    delay(100);
  }
}

void setup() {
  // Set pin modes
  pinMode(SMS_RECEIVED_PIN, OUTPUT);
  pinMode(SMS_SENT_PIN, OUTPUT);
  pinMode(RELAY_PIN, OUTPUT);

  // Turn off all LEDs and relay initially
  digitalWrite(SMS_RECEIVED_PIN, LOW);
  digitalWrite(SMS_SENT_PIN, LOW);
  digitalWrite(RELAY_PIN, LOW);

  // Initialize serial communication
  Serial.begin(115200);
  while (!Serial) { }
  Serial.println("Serial initialized!");

  // Initialize SIM800L communication on UART1
  sim800.begin(9600, SERIAL_8N1, RX_PIN, TX_PIN);
  delay(5000);

  blinkLED(SMS_RECEIVED_PIN);
  blinkLED(SMS_SENT_PIN);
  blinkLED(RELAY_PIN);
  delay(1000);


  sim800.println("AT");
  delay(1000);  // wait for the response
  while (sim800.available()) {
      Serial.write(sim800.read());  // print the response to the serial monitor
  }

  Serial.println("SIM800L initialized!");

  // Set SMS mode to text
  sim800.println("AT+CMGF=1");
  delay(1000);

  // Set SMS notifications
  sim800.println("AT+CNMI=1,2,0,0,0");
  delay(10000);
}




void loop() {
    static String buffer = ""; // Buffer for incoming data

    // Check if data is available from the SIM800 module
    while (sim800.available()) {
        char c = sim800.read();
        buffer += c;

        // Detect the end of a message (newline character)
        if (c == '\n') {
            Serial.println("Encrypted SMS:");
            Serial.println(buffer);

            // Process the received SMS
            processSMS(buffer);
        }

        // Prevent the buffer from growing excessively (sanity check)
        if (buffer.length() > 1024) {
            Serial.println("Buffer overflow detected! Clearing buffer.");
            buffer = "";
        }
    }
}







