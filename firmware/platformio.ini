; PlatformIO Project Configuration File
;
; Build options: build flags, source filter
; Upload options: custom upload port, speed, and extra flags
; Library options: dependencies, extra library storages
; Advanced options: extra scripting
;
; Please visit documentation for the other options and examples:
; https://docs.platformio.org/page/projectconf.html

[env:ttgo-t-call]
platform = espressif32
board = ttgo-t1
framework = arduino

; Specify the upload port and speed
upload_port = /dev/ttyUSB1
monitor_port = /dev/ttyUSB1
monitor_speed = 115200
upload_speed = 115200

; Set build type for debugging
build_type = debug

; Specify library dependencies
lib_deps =
    TinyGSM
    Adafruit GFX Library
    Adafruit BusIO
    mbedtls

; Include build flags
build_flags =
    -D ARDUINO_HAS_HWSERIAL
    -D USE_MBEDTLS

; Ensure compatibility with TinyGSM
; Avoid using SoftwareSerial for ESP32
