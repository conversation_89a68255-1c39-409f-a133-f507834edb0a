from database import mongo
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

def attach_phone_number_to_device(token, phone_number):
    update_data = {
        "$set": {
            "phone_number": phone_number,
        }
    }
    result = mongo.db.devices.update_one({"iot_token": token}, update_data)
    if result.modified_count > 0:
        return {"message": SUCCESS_PHONE_NUMBER_ADDED}
    return {"error": ERROR_NO_PHONE_NUMBER_ADDED}

def turn_status_off(token):
    update_data = {
        "$set": {"status": False}
    }
    result = mongo.db.devices.update_one({"iot_token": token}, update_data)
    if result.modified_count > 0:
        return {"message": SUCCESS_STATUS_SET_TO_OFF}
    return {"error": ERROR_STATUS_DID_NOT_CHANGE}

def turn_status_on(token):
    update_data = {
        "$set": {"status": True}
    }
    result = mongo.db.devices.update_one({"iot_token": token}, update_data)
    if result.modified_count > 0:
        return {"message": SUCCESS_STATUS_SET_TO_ON}
    return {"error": ERROR_STATUS_DID_NOT_CHANGE}

def check_connection():
    try:
        mongo.db.command("ping")
    except (ConnectionFailure, ServerSelectionTimeoutError):
        raise ConnectionError("Database connection is unavailable.")

def get_device_status(token):
    try:
        check_connection()
        device = mongo.db.devices.find_one({"token": token})
        return device.get("status") if device else None
    except ConnectionError as e:
        return {"error": str(e)}

def update_device_status(token, status):
    try:
        check_connection()
        result = mongo.db.devices.update_one({"token": token}, {"$set": {"status": status}})
        return result.modified_count > 0
    except ConnectionError as e:
        return {"error": str(e)}

def add_schedule(token, schedule_data):
    try:
        check_connection()
        result = mongo.db.devices.update_one({"token": token}, {"$set": {"schedule": schedule_data}})
        return result.modified_count > 0
    except ConnectionError as e:
        return {"error": str(e)}

def fetch_history(token):
    try:
        check_connection()
        device = mongo.db.devices.find_one({"token": token})
        return device.get("history") if device else None
    except ConnectionError as e:
        return {"error": str(e)}
