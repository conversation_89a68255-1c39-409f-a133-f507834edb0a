from database import mongo
from werkzeug.security import generate_password_hash, check_password_hash
from constants import (
    ERROR_INVALID_TOKEN, 
    ERROR_NO_CHANGE_MADE, 
    ERROR_TOKEN_HAS_TAKEN, 
    SUCCESS_SIGNED_IN, 
    SUCCESS_USER_UPDATED
)

def create_user(token, email, password):
    existing_user = mongo.db.users.find_one({"iot_token": token})
    if not existing_user:
        return {"message": ERROR_INVALID_TOKEN}, 400
    if "email" in existing_user or "web_token" in existing_user:
        return {"message": ERROR_TOKEN_HAS_TAKEN}, 400
    web_token = generate_password_hash(password)
    update_data = {
        "$set": {
            "email": email,
            "web_token": web_token,
        }
    }
    result = mongo.db.users.update_one({"iot_token": token}, update_data)
    if result.modified_count > 0:
        return {"message": SUCCESS_USER_UPDATED, "token": web_token}
    return {"error": ERROR_NO_CHAN<PERSON>_MADE}

async def authenticate_user(email, password):
    user = mongo.db.users.find_one({"email": email})
    web_token = user.get("web_token")
    if user and check_password_hash(web_token, password):
        return {"message": SUCCESS_SIGNED_IN, "token": web_token}
    return None