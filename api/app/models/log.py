from database import mongo
from datetime import timezone

def store_log(description, token):
    log_entry = {
        "description": description,
        "iot_token": token,
        "timestamp": timezone.utcnow()
    }
    result = mongo.db.logs.insert_one(log_entry)
    return result.inserted_id is not None


def fetch_logs(limit, skip=0, token=None):
    logs = (
        mongo.db.logs.find({"web_token": token})
        .sort("timestamp", -1)
        .skip(skip)
        .limit(limit)
    )
    return [
        {
            "description": log.get("description"), 
            "timestamp": log.get("timestamp")
        } 
        for log in logs
    ]
