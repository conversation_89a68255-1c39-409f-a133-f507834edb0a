from models.device import (
    attach_phone_number_to_device, 
    turn_status_off,
    turn_status_on, 
)

def set_phone_number(token, phone_number):
    return attach_phone_number_to_device(
        token=token, 
        phone_number=phone_number
    )

def set_status_on(token):
    return turn_status_on(token=token)

def set_status_off(token):
    return turn_status_off(token=token)

async def read_device_status(token):
    # Ask device the status
    print("Ask device the status")

async def switch_device_on(token):
    print("switch on")
    set_status_on(token)

async def switch_device_off(token):
    print("switch off")
    set_status_off(token)