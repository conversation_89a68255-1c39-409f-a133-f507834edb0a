from pymongo import MongoClient

class Database:
    def __init__(self, uri: str, db_name: str):
        self.client = MongoClient(uri)
        self.db = self.client[db_name]
        self.initialize_collections()

    def initialize_collections(self):
        # Ensure 'users' and 'devices' collections are created
        self.create_collection_if_not_exists('users')
        self.create_collection_if_not_exists('devices')
        self.create_collection_if_not_exists('logs')

    def create_collection_if_not_exists(self, collection_name: str):
        if collection_name not in self.db.list_collection_names():
            # Insert a dummy document to create the collection
            self.db[collection_name].insert_one({"_init": True})
            print(f"Collection '{collection_name}' created.")
            # Optionally remove the dummy document
            self.db[collection_name].delete_one({"_init": True})

    def get_collection(self, collection_name: str):
        return self.db[collection_name]

# Initialize the database and collections
mongo = Database(
    uri="mongodb://localhost:27017",
    db_name="mrc"
)
