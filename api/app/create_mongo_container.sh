#!/bin/bash

# Variables
CONTAINER_NAME="mongodb_container"
IMAGE_NAME="mrc_mongodb"
PORT=27017

# Step 1: Build the Docker image
echo "Building the Docker image..."
docker build -t $IMAGE_NAME .

# Step 2: Check if the container already exists
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
    echo "A container with the name $CONTAINER_NAME already exists."

    # Stop and remove the existing container
    echo "Stopping and removing the existing container..."
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
fi

# Step 3: Run the MongoDB container
echo "Running the MongoDB container..."
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:27017 \
    $IMAGE_NAME

echo "MongoDB container is up and running."
