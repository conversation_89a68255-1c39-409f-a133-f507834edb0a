from functools import wraps
from flask import request, jsonify
from utils.validators import is_valid_email, is_valid_password
from services.auth import validate_iot_token, validate_web_token
from constants import (
    ERROR_EMAIL_FORMAT_INVALID, 
    ERROR_PASSWORD_FORMAT_INVALID, 
    ERROR_TOKEN_REQUIRED, 
    ERROR_DEVICE_NOT_FOUND, 
    ERROR_USER_NOT_FOUND
)

def extract_param(param_name, *args, **kwargs):
    """Extract a parameter (e.g., token) from request.args, request.json, kwargs, or *args."""
    # Check in kwargs
    param = kwargs.get(param_name)
    if param is not None:
        return param

    # Check in request.json
    if request.json:
        param = request.json.get(param_name)
        if param is not None:
            return param

    # Check in request.args
    param = request.args.get(param_name)
    if param is not None:
        return param

    # Check in *args
    for arg in args:
        if isinstance(arg, dict) and param_name in arg:
            return arg[param_name]

    return None

def iot_token_required(func):
    @wraps(func)
    def decorated_function(*args, **kwargs):
        token = extract_param('token', *args, **kwargs)
        if not token:
            return jsonify({"error": ERROR_TOKEN_REQUIRED}), 400
        
        # Validate the token with the database
        if not validate_iot_token(token):
            return jsonify({"error": ERROR_DEVICE_NOT_FOUND}), 404

        return func(*args, **kwargs)
    return decorated_function

def web_token_required(func):
    @wraps(func)
    def decorated_function(*args, **kwargs):
        token = extract_param('token', *args, **kwargs)
        if not token:
            return jsonify({"error": ERROR_TOKEN_REQUIRED}), 400
        
        # Validate the token with the database
        if not validate_web_token(token):
            return jsonify({"error": ERROR_USER_NOT_FOUND}), 404

        return func(*args, **kwargs)
    return decorated_function

def validate_email(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        email = extract_param('email', *args, **kwargs)
        if not email or not is_valid_email(email):
            return jsonify({"error": ERROR_EMAIL_FORMAT_INVALID}), 404
        return func(*args, **kwargs)
    return wrapper

def validate_password(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        password = extract_param('password', *args, **kwargs)
        if not password or not is_valid_password(password):
            return jsonify({"error": ERROR_PASSWORD_FORMAT_INVALID}), 404
        return func(*args, **kwargs)
    return wrapper