import re

def is_valid_email(email):
    """
    Validate the email format.

    Email Structure:
    1. Local Part:
       - Begins with alphanumeric characters (a-z, A-Z, 0-9).
       - Can include special characters such as ".", "_", "%", "+", and "-".
       - Special characters must not be at the start or end and cannot appear consecutively.

    2. "@" Symbol:
       - Separates the local part and the domain part.
       - Must appear exactly once.

    3. Domain Part:
       - Contains alphanumeric characters and hyphens.
       - Must not start or end with a hyphen.
       - Must have at least one dot separating the domain and the top-level domain (TLD).

    4. Top-Level Domain (TLD):
       - Must be at least 2 characters long.
       - Consists of only alphabetic characters.

    Regex Explanation:
    ^[a-zA-Z0-9._%+-]+     # Local part: Starts with alphanumeric, allows certain special characters
    @[a-zA-Z0-9.-]+        # Domain part: After @, allows alphanumeric, dots, and hyphens
    \.[a-zA-Z]{2,}$        # Top-level domain: Starts with a dot and has at least 2 alphabetic characters
    """
    email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(email_regex, email))


def is_valid_password(password):
    """Validate the password strength."""
    # Example password requirements:
    # - At least 8 characters
    # - Contains at least one uppercase letter
    # - Contains at least one lowercase letter
    # - Contains at least one digit
    # - Contains at least one special character
    password_regex = (
        r"^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
    )
    return bool(re.match(password_regex, password))