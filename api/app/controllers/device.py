from flask import Blueprint, request, jsonify
from api.app.services.notification import send_notification
from utils.decorators import iot_token_required, web_token_required
from services.log import log_event
from constants import (
    ERROR_FAILED_TO_SCHEDULE,
    ERROR_FAILED_TO_TURN_OFF,
    ERROR_FAILED_TO_TURN_ON,
    ERROR_POWER_EVENT_REQUIRED,
    ERROR_SCHEDULE_REQUIRED,
    LOG_POWER_BACK_ON,
    LOG_POWER_WENT_OUT,
    SUCCESS_DEVICE_TURNED_OFF,
    SUCCESS_DEVICE_TURNED_ON,
    SUCCESS_OPERATION_SCHEDULED,
    LOG_POWER_WENT_OUT,
)
from services.device import (
    read_device_status,
    set_phone_number,
    set_status_off,
    set_status_on,
    schedule_device,
    switch_device_off,
    switch_device_on, 
)

device_bp = Blueprint("device", __name__)

# Requests from App
@device_bp.route("/status", methods=["GET"])
@web_token_required
async def get_status():
    token = request.args.get("token")
    
    # Check device status
    status = await read_device_status(token)
    if not status:
        return jsonify({"error": ERROR_DEVICE_IS_NOT_RESPONDING}), 500
    
    # Determine if the device is on
    is_device_on = status.get('status')
    
    # Update status and log events based on device state
    if is_device_on:
        set_status_on(token=token)
        LOG_DESCRIPTION = LOG_STATUS_CHECKED_WAS_ON
    else:
        set_status_off(token=token)
        LOG_DESCRIPTION = LOG_STATUS_CHECKED_WAS_OFF
    log_event(token=token, description=LOG_DESCRIPTION)
    
    return jsonify({"status": status}), 200


@device_bp.route("/turn_on", methods=["POST"])
@web_token_required
def turn_on():
    data = request.json
    token = data.get("token")
    success = switch_device_on(token)
    if not success:
        return jsonify({"error": ERROR_DEVICE_IS_NOT_RESPONDING}), 500
    return jsonify({"message": SUCCESS_DEVICE_TURNED_ON}), 200

@device_bp.route("/turn_off", methods=["POST"])
@web_token_required
async def turn_off():
    data = request.json
    token = data.get("token")
    success = await switch_device_off(token)
    if not success:
        return jsonify({"error": ERROR_DEVICE_IS_NOT_RESPONDING}), 500
    return jsonify({"message": SUCCESS_DEVICE_TURNED_OFF}), 200

@device_bp.route("/schedule", methods=["POST"])
@web_token_required
def schedule():
    data = request.json
    token = data.get("token")
    schedule_data = data.get("schedule")
    if schedule_data:
        return jsonify({"error": ERROR_SCHEDULE_REQUIRED}), 400
    success = schedule_device(
        token=token, 
        schedule_data=schedule_data
    )
    if not success:
        return jsonify({"error": ERROR_FAILED_TO_SCHEDULE}), 500
    return jsonify({"message": SUCCESS_OPERATION_SCHEDULED}), 200

# Requests from IoT
@device_bp.route("/phone_number", methods=["POST"])
@iot_token_required
def phone_number():
    data = request.json
    token = data.get('token')
    phone_number = data.get('phone_number')
    if not data or not phone_number:
        return jsonify({"error": ERROR_PHONE_NUMBER_REQUIRED}), 400
    set_phone_number(token=token, phone_number=phone_number)
    log_event(token=token, description=LOG_PHONE_NUMBER_SET)
    return jsonify({"message": LOG_PHONE_NUMBER_ADDED}), 201

@device_bp.route("/power_went_out", methods=["POST"])
@iot_token_required
def power_went_out():
    data = request.json
    token = data.get("token")
    event = data.get("event")
    if not data or not event:
        return jsonify({"error": ERROR_POWER_EVENT_REQUIRED}), 400
    set_status_off(token=token)
    send_notification(token=token, message=NOTIFICATION_POWER_WENT_OUT)
    log_event(token=token, description=LOG_POWER_WENT_OUT)
    return jsonify({"message": LOG_POWER_WENT_OUT}), 201

@device_bp.route("/power_back_on", methods=["POST"])
@iot_token_required
def power_back_on():
    data = request.json
    token = data.get("token")
    event = data.get("event")
    if not data or not event:
        return jsonify({"error": ERROR_POWER_EVENT_REQUIRED}), 400
    set_status_on(token=token)
    send_notification(token=token, message=NOTIFICATION_POWER_BACK_ON)
    log_event(token=token, description=LOG_POWER_BACK_ON)
    return jsonify({"message": LOG_POWER_BACK_ON}), 201