from flask import Blueprint, request, jsonify
from utils.decorators import iot_token_required, validate_email, validate_password
from services.user import sign_up_user, sign_in_user
from constants import (
    ERROR_EMAIL_PASSWORD_REQUIRED,
    ERROR_USER_CREATION_FAILED,
    ERROR_INVALID_CREDENTIALS,
)

user_bp = Blueprint("user", __name__)

@user_bp.route("/signup", methods=["POST"])
@validate_email
@validate_password
@iot_token_required
async def sign_up():
    data = request.json
    token = data.get('token')
    email = data.get('email')
    password = data.get('password')
    if not email or not password or not token:
        return jsonify({"error": ERROR_EMAIL_PASSWORD_REQUIRED}), 400
    user = sign_up_user(token=token, email=email, password=password)
    if not user:
        return jsonify({"error": ERROR_USER_CREATION_FAILED}), 500
    return jsonify({"user": user}), 201

@user_bp.route("/signin", methods=["POST"])
@validate_email
@validate_password
async def sign_in():
    data = request.json
    email = data.get("email")
    password = data.get("password")
    if not email or not password:
        return jsonify({"error": ERROR_EMAIL_PASSWORD_REQUIRED}), 400
    user = await sign_in_user(data)
    if not user:
        return jsonify({"error": ERROR_INVALID_CREDENTIALS}), 401
    return jsonify({"user": user}), 200