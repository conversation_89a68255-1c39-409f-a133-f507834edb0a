from flask import Blueprint, request, jsonify
from api.app.constants import ERROR_NO_HISTORY_FOUND
from services.log import get_logs
from utils.decorators import web_token_required

log_bp = Blueprint("log", __name__)

@log_bp.route("/history", methods=["GET"])
@web_token_required
async def fetch_history():
    data = request.args
    skip = data.get('skip')
    limit = data.get('limit')
    web_token = data.get('token')
    
    history = await get_logs(token=web_token, skip=skip, limit=limit)
    
    if not history:
        return jsonify({"error": ERROR_NO_HISTORY_FOUND}), 404

    return jsonify({"history": history}), 200
