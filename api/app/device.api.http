POST http://127.0.0.1:5000/device/status HTTP/1.1
content-type: application/json

{
    "token": "web_token"
}

###

POST http://127.0.0.1:5000/device/turn_on HTTP/1.1
content-type: application/json

{
    "token": "web_token"
}

###

POST http://127.0.0.1:5000/device/turn_off HTTP/1.1
content-type: application/json

{
    "token": "web_token"
}

###

POST http://127.0.0.1:5000/device/schedule HTTP/1.1
content-type: application/json

{
    "token": "web_token",
    "schedule": "list of dates with on and off key"
}

###

POST http://127.0.0.1:5000/device/history HTTP/1.1
content-type: application/json

{
    "token": "web_token"
}

###

POST http://127.0.0.1:5000/device/ HTTP/1.1
content-type: application/json

{
    "token": "web_token"
}