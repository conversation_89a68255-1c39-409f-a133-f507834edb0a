ERROR_TOKEN_REQUIRED = "Token is required"
ERROR_DEVICE_NOT_FOUND = "Device not found"
ERROR_FAILED_TO_TURN_ON = "Failed to turn on the device"
ERROR_FAILED_TO_TURN_OFF = "Failed to turn off the device"
ERROR_SCHEDULE_REQUIRED = "Schedule data are required"
ERROR_FAILED_TO_SCHEDULE = "Failed to schedule device operation"
ERROR_NO_HISTORY_FOUND = "No history found"
ERROR_POWER_EVENT_REQUIRED = "Power event data is required"
ERROR_EMAIL_PASSWORD_REQUIRED = "Email and password are required"
ERROR_USER_CREATION_FAILED = "User creation failed!"
ERROR_INVALID_CREDENTIALS = "Invalid credentials!"
ERROR_INVALID_TOKEN = "Invalid token!"
ERROR_INVALID_TOKEN = "Invalid token!"
ERROR_TOKEN_HAS_TAKEN = "IoT has already taken!"
ERROR_USER_NOT_FOUND = "User not found!"
ERROR_NO_CHANGE_MADE = "No changes made!"
ERROR_EMAIL_FORMAT_INVALID = "Email format is invalid!"
ERROR_PASSWORD_FORMAT_INVALID = "Password format is invalid!"

SUCCESS_SIGNED_IN = "Sign-in successful"
SUCCESS_DEVICE_TURNED_ON = "Device turned on successfully"
SUCCESS_DEVICE_TURNED_OFF = "Device turned off successfully"
SUCCESS_OPERATION_SCHEDULED = "Device operation scheduled successfully"
SUCCESS_USER_UPDATED = "User updated successfully"

LOG_POWER_WENT_OUT = "Power went out"
LOG_POWER_BACK_ON = "Power back on"