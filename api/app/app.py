from flask import Flask

# Import Blueprints
from controllers.log import log_bp
from controllers.user import user_bp
from controllers.device import device_bp

# Create the Flask application
app = Flask(__name__)

# Register blueprints
app.register_blueprint(log_bp, url_prefix="/log")
app.register_blueprint(user_bp, url_prefix="/user")
app.register_blueprint(device_bp, url_prefix="/device")

if __name__ == "__main__":
    app.run(debug=True)
