├── api/ # Python API service
│ ├── app/ # Main application directory
│ │ ├── controllers/ # API endpoint handlers
│ │ ├── models/ # Database models
│ │ ├── services/ # Business logic
│ │ ├── utils/ # Utility functions
│ │ └── app.py # Entry point of the Flask/FastAPI app
│ ├── migrations/ # Database migration files
│ ├── tests/ # Unit and integration tests
│ ├── requirements.txt # Python dependencies
│ └── README.md # Overview of API service development
