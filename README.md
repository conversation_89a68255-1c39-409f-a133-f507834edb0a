# Project Structure: MR-C

```
MR-C/
├── hardware/
│   ├── 3d-models/       # 3D design files for the physical parts
│   ├── pcb-design/      # PCB design files (e.g., KiCad, Eagle files)
│   ├── docs/            # Documentation for hardware assembly and BOM
│   └── README.md        # Overview of hardware development
├── firmware/
│   ├── esp32-code/      # Codebase for ESP32 microcontroller
│   │   ├── src/         # Source code
│   │   ├── include/     # Header files
│   │   ├── lib/         # External libraries
│   │   └── platformio.ini # PlatformIO configuration for ESP32
│   └── README.md        # Overview of firmware development
├── app/                 # Progressive Web App
│   ├── public/          # Static assets (images, icons, etc.)
│   ├── src/             # Source code
│   │   ├── components/  # Reusable React components
│   │   ├── hooks/       # Custom hooks
│   │   ├── pages/       # Page components
│   │   ├── services/    # API service functions
│   │   ├── styles/      # CSS/Tailwind styles
│   │   ├── App.js       # Main App component
│   │   ├── index.js     # Entry point
│   │   └── config.js    # Configuration (e.g., API base URL)
│   ├── package.json     # Node.js project metadata
│   └── README.md        # Overview of PWA development
├── api/                 # Python API service
│   ├── app/             # Main application directory
│   │   ├── controllers/ # API endpoint handlers
│   │   ├── models/      # Database models
│   │   ├── services/    # Business logic
│   │   ├── utils/       # Utility functions
│   │   └── app.py       # Entry point of the Flask/FastAPI app
│   ├── migrations/      # Database migration files
│   ├── tests/           # Unit and integration tests
│   ├── requirements.txt # Python dependencies
│   └── README.md        # Overview of API service development
├── docs/                # General project documentation
│   ├── architecture.md  # Project architecture overview
│   ├── setup.md         # Setup instructions
│   └── README.md        # Main project documentation
├── .gitignore           # Ignore unnecessary files for version control
├── LICENSE              # License file for the project
└── README.md            # Main README for the project readable in GITLAB website
```

# Design

https://www.notion.so/MR-C-16434b22f17d801ab5ccc38557a33cfc?pvs=4
